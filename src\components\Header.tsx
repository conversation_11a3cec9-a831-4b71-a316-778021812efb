
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Menu, Phone, Mail, Home, ArrowRight, Sparkles, Users, MessageCircle } from "lucide-react";
import { Link, useLocation } from "react-router-dom";

export const Header = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: "Services", href: "/#services", isExternal: true },
    { name: "How It Works", href: "/how-it-works", isExternal: false },
    { name: "About", href: "/about", isExternal: false },
    { name: "Contact", href: "/contact", isExternal: false },
  ];

  const handleNavClick = (href: string, isExternal: boolean) => {
    if (isExternal && href.includes("#")) {
      // Handle anchor links
      const element = document.querySelector(href.split("#")[1]);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
    setIsOpen(false);
  };

  return (
    <header className="border-b border-primary/10 bg-white/95 backdrop-blur-sm supports-[backdrop-filter]:bg-white/90 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3 group">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
              <Home className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              KlikaHelper
            </h1>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
            {navigation.map((item) => (
              item.isExternal ? (
                <a
                  key={item.name}
                  href={item.href}
                  className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors relative group"
                  onClick={(e) => {
                    if (item.href.includes("#")) {
                      e.preventDefault();
                      handleNavClick(item.href, true);
                    }
                  }}
                >
                  {item.name}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </a>
              ) : (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`text-sm font-medium transition-colors relative group ${
                    location.pathname === item.href
                      ? "text-primary"
                      : "text-muted-foreground hover:text-primary"
                  }`}
                >
                  {item.name}
                  <span className={`absolute -bottom-1 left-0 h-0.5 bg-primary transition-all duration-300 ${
                    location.pathname === item.href ? "w-full" : "w-0 group-hover:w-full"
                  }`}></span>
                </Link>
              )
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2 hover:text-primary transition-colors cursor-pointer">
                <Phone className="h-4 w-4 text-primary" />
                <span className="font-medium">(*************</span>
              </div>
              <div className="flex items-center gap-2 hover:text-primary transition-colors cursor-pointer">
                <Mail className="h-4 w-4 text-primary" />
                <span className="font-medium"><EMAIL></span>
              </div>
            </div>
            <Link to="/#services">
              <Button className="hidden md:flex bg-primary hover:bg-primary/90 text-white shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Book Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>

            {/* Mobile Menu */}
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button className="md:hidden" variant="outline" size="icon">
                  <Menu className="h-5 w-5 text-primary" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-full sm:w-80 bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50 border-l border-primary/10 overflow-y-auto">
                <SheetHeader className="text-left">
                  <SheetTitle className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
                      <Home className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                      KlikaHelper
                    </span>
                  </SheetTitle>
                  <SheetDescription className="text-gray-600">
                    Your trusted home helper network
                  </SheetDescription>
                </SheetHeader>

                <div className="mt-8 space-y-6">
                  {/* Navigation Links */}
                  <div className="space-y-4">
                    {navigation.map((item, index) => (
                      item.isExternal ? (
                        <a
                          key={item.name}
                          href={item.href}
                          className="flex items-center gap-4 p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-blue-100 hover:border-primary/30 hover:bg-white/80 transition-all duration-300 group"
                          onClick={(e) => {
                            if (item.href.includes("#")) {
                              e.preventDefault();
                              handleNavClick(item.href, true);
                            }
                          }}
                        >
                          <div className="w-10 h-10 bg-gradient-to-r from-primary to-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            {index === 0 && <Sparkles className="h-5 w-5 text-white" />}
                          </div>
                          <div>
                            <div className="font-semibold text-gray-800 group-hover:text-primary transition-colors">
                              {item.name}
                            </div>
                            <div className="text-sm text-gray-600">
                              {index === 0 && "Browse our services"}
                            </div>
                          </div>
                          <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200 ml-auto" />
                        </a>
                      ) : (
                        <Link
                          key={item.name}
                          to={item.href}
                          className="flex items-center gap-4 p-4 rounded-2xl bg-white/60 backdrop-blur-sm border border-blue-100 hover:border-primary/30 hover:bg-white/80 transition-all duration-300 group"
                          onClick={() => setIsOpen(false)}
                        >
                          <div className="w-10 h-10 bg-gradient-to-r from-primary to-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                            {index === 1 && <Users className="h-5 w-5 text-white" />}
                            {index === 2 && <Home className="h-5 w-5 text-white" />}
                            {index === 3 && <MessageCircle className="h-5 w-5 text-white" />}
                          </div>
                          <div>
                            <div className="font-semibold text-gray-800 group-hover:text-primary transition-colors">
                              {item.name}
                            </div>
                            <div className="text-sm text-gray-600">
                              {index === 1 && "Learn our process"}
                              {index === 2 && "Our story & values"}
                              {index === 3 && "Get in touch"}
                            </div>
                          </div>
                          <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-primary group-hover:translate-x-1 transition-all duration-200 ml-auto" />
                        </Link>
                      )
                    ))}
                  </div>

                  {/* Contact Info */}
                  <div className="space-y-4 pt-6 border-t border-primary/10">
                    <div className="flex items-center gap-3 p-3 rounded-xl bg-white/40 backdrop-blur-sm">
                      <Phone className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium text-gray-800">(*************</div>
                        <div className="text-sm text-gray-600">24/7 Support</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 rounded-xl bg-white/40 backdrop-blur-sm">
                      <Mail className="h-5 w-5 text-primary" />
                      <div>
                        <div className="font-medium text-gray-800"><EMAIL></div>
                        <div className="text-sm text-gray-600">Quick Response</div>
                      </div>
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Link to="/#services" onClick={() => setIsOpen(false)}>
                    <Button className="w-full h-14 text-lg font-bold bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                      Book a Service Now
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  );
};
