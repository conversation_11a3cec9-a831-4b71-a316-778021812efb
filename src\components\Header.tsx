
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>u, Phone, Mail, Home } from "lucide-react";

export const Header = () => {
  return (
    <header className="border-b border-primary/10 bg-white/95 backdrop-blur-sm supports-[backdrop-filter]:bg-white/90 sticky top-0 z-50 shadow-sm">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center">
              <Home className="h-5 w-5 text-white" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              KlikaHelper
            </h1>
          </div>

          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
            <a href="#services" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
              Services
            </a>
            <a href="#how-it-works" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
              How It Works
            </a>
            <a href="#about" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
              About
            </a>
            <a href="#contact" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
              Contact
            </a>
          </nav>

          <div className="flex items-center space-x-4">
            <div className="hidden lg:flex items-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-primary" />
                <span className="font-medium">(*************</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-primary" />
                <span className="font-medium"><EMAIL></span>
              </div>
            </div>
            <Button variant="outline" className="hidden md:flex text-primary border-primary hover:bg-primary hover:text-white transition-colors">
              Sign In
            </Button>
            <Button className="hidden md:flex bg-primary hover:bg-primary/90 text-white">
              Book Now
            </Button>
            <Button className="md:hidden" variant="outline" size="icon">
              <Menu className="h-5 w-5 text-primary" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};
