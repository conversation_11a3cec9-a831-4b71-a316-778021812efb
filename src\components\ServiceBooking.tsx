
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, ArrowLeft, CheckCircle, Mail, Phone, MapPin, Clock, Star, Sparkles, Shield } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";

interface Service {
  id: string;
  title: string;
  description: string;
  price: string;
  icon?: any;
  gradient?: string;
}

interface ServiceBookingProps {
  service: Service;
  onBack: () => void;
}

export const ServiceBooking = ({ service, onBack }: ServiceBookingProps) => {
  const [step, setStep] = useState(1);
  const [date, setDate] = useState<Date>();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    time: "",
    duration: "",
    notes: ""
  });
  const { toast } = useToast();

  const handleSubmit = () => {
    // Create mailto link with booking details
    const subject = `KlikaHelper Booking Request - ${service.title}`;
    const body = `
Hello KlikaHelper Team,

I would like to book the following service:

Service: ${service.title}
Date: ${date ? format(date, "PPP") : "Not selected"}
Time: ${formData.time}
Duration: ${formData.duration}

Contact Information:
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Service Address: ${formData.address}

Special Instructions:
${formData.notes || "None"}

Please confirm this booking and let me know the next steps.

Thank you!
${formData.name}
    `.trim();

    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;

    toast({
      title: "Email Client Opened!",
      description: "Please send the email to complete your booking request.",
    });
    setStep(3);
  };

  const timeSlots = [
    "8:00 AM", "9:00 AM", "10:00 AM", "11:00 AM",
    "12:00 PM", "1:00 PM", "2:00 PM", "3:00 PM",
    "4:00 PM", "5:00 PM", "6:00 PM"
  ];

  const durations = service.id === "babysitting"
    ? ["2 hours", "4 hours", "6 hours", "8 hours", "Full day"]
    : service.id === "cleaning"
    ? ["2 hours", "3 hours", "4 hours", "5 hours", "Full day cleaning"]
    : ["2 hours", "3 hours", "4 hours", "6 hours", "Full day"];

  if (step === 3) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50 flex items-center justify-center">
        <div className="container mx-auto px-4">
          <div className="max-w-lg mx-auto text-center">
            {/* Animated Success Icon */}
            <div className="relative mb-8">
              <div className="w-24 h-24 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full flex items-center justify-center mx-auto shadow-2xl animate-bounce">
                <CheckCircle className="h-12 w-12 text-white" />
              </div>
              <div className="absolute inset-0 w-24 h-24 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full mx-auto animate-ping opacity-20"></div>
            </div>

            <h1 className="text-4xl font-black text-gray-800 mb-4">
              Email Sent!
            </h1>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed">
              Your booking request has been sent to <span className="font-semibold text-primary"><EMAIL></span>.
              We'll contact you within 2 hours to confirm your booking details.
            </p>

            <div className="bg-white/80 backdrop-blur-sm border border-blue-100 rounded-2xl p-6 mb-8 shadow-lg">
              <div className="flex items-center justify-center gap-3 mb-4">
                <Mail className="h-6 w-6 text-primary" />
                <span className="font-semibold text-gray-800">What happens next?</span>
              </div>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>We'll review your booking request</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Match you with a verified helper</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span>Send confirmation with helper details</span>
                </div>
              </div>
            </div>

            <Button
              onClick={onBack}
              className="w-full bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 text-white font-bold py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Book Another Service
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50">
      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gradient-to-r from-sky-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-4 py-8 relative z-10">
        <div className="max-w-4xl mx-auto">
          <Button
            variant="ghost"
            onClick={onBack}
            className="mb-8 text-primary hover:text-primary/80 hover:bg-primary/10"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Services
          </Button>

          {/* Service Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-4 mb-6">
              {service.icon && (
                <div className={`w-16 h-16 bg-gradient-to-r ${service.gradient || 'from-primary to-blue-500'} rounded-2xl flex items-center justify-center shadow-lg`}>
                  <service.icon className="h-8 w-8 text-white" />
                </div>
              )}
              <div>
                <h1 className="text-4xl font-black text-gray-800">Book {service.title}</h1>
                <p className="text-lg text-gray-600 mt-2">{service.description}</p>
              </div>
            </div>

            {/* Progress Indicator */}
            <div className="flex items-center justify-center gap-4 mb-8">
              <div className={`flex items-center gap-2 ${step >= 1 ? 'text-primary' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-primary text-white' : 'bg-gray-200'} transition-all duration-300`}>
                  {step > 1 ? <CheckCircle className="h-4 w-4" /> : '1'}
                </div>
                <span className="font-medium">Date & Time</span>
              </div>
              <div className={`w-12 h-0.5 ${step >= 2 ? 'bg-primary' : 'bg-gray-200'} transition-all duration-300`}></div>
              <div className={`flex items-center gap-2 ${step >= 2 ? 'text-primary' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-primary text-white' : 'bg-gray-200'} transition-all duration-300`}>
                  {step > 2 ? <CheckCircle className="h-4 w-4" /> : '2'}
                </div>
                <span className="font-medium">Contact Info</span>
              </div>
            </div>
          </div>

          <Card className="bg-white/80 backdrop-blur-xl border-blue-100 shadow-2xl rounded-3xl overflow-hidden">
            <CardHeader className="bg-gradient-to-r from-primary/5 to-blue-500/5 border-b border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-2xl text-gray-800">
                    {step === 1 ? 'Select Your Preferred Time' : 'Your Contact Information'}
                  </CardTitle>
                  <CardDescription className="text-gray-600 mt-2">
                    {step === 1
                      ? 'Choose when you\'d like our helper to arrive'
                      : 'We\'ll use this information to confirm your booking'
                    }
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-500">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span>4.9/5 rating</span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-8 space-y-8">
            {step === 1 && (
              <div className="space-y-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Date Selection */}
                  <div className="space-y-4">
                    <div className="flex items-center gap-2 mb-4">
                      <CalendarIcon className="h-5 w-5 text-primary" />
                      <Label className="text-lg font-semibold text-gray-800">Preferred Date</Label>
                    </div>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal h-14 text-base border-2 hover:border-primary/50 transition-colors",
                            !date && "text-muted-foreground",
                            date && "border-primary/30 bg-primary/5"
                          )}
                        >
                          <CalendarIcon className="mr-3 h-5 w-5" />
                          {date ? format(date, "EEEE, MMMM do, yyyy") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={date}
                          onSelect={setDate}
                          disabled={(date) => date < new Date()}
                          initialFocus
                          className="rounded-xl border-0"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  {/* Time & Duration */}
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Clock className="h-5 w-5 text-primary" />
                        <Label className="text-lg font-semibold text-gray-800">Preferred Time</Label>
                      </div>
                      <Select value={formData.time} onValueChange={(value) => setFormData({...formData, time: value})}>
                        <SelectTrigger className="h-14 text-base border-2 hover:border-primary/50 transition-colors">
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                        <SelectContent>
                          {timeSlots.map((time) => (
                            <SelectItem key={time} value={time} className="text-base py-3">{time}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5 text-primary" />
                        <Label className="text-lg font-semibold text-gray-800">Duration</Label>
                      </div>
                      <Select value={formData.duration} onValueChange={(value) => setFormData({...formData, duration: value})}>
                        <SelectTrigger className="h-14 text-base border-2 hover:border-primary/50 transition-colors">
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                        <SelectContent>
                          {durations.map((duration) => (
                            <SelectItem key={duration} value={duration} className="text-base py-3">{duration}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Service Summary */}
                {date && formData.time && formData.duration && (
                  <div className="bg-gradient-to-r from-primary/5 to-blue-500/5 border border-primary/20 rounded-2xl p-6 animate-in slide-in-from-bottom-4 duration-500">
                    <h4 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-emerald-500" />
                      Booking Summary
                    </h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Service:</span>
                        <p className="font-medium text-gray-800">{service.title}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Date:</span>
                        <p className="font-medium text-gray-800">{format(date, "MMM do, yyyy")}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Time:</span>
                        <p className="font-medium text-gray-800">{formData.time}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Duration:</span>
                        <p className="font-medium text-gray-800">{formData.duration}</p>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  onClick={() => setStep(2)}
                  className="w-full h-14 text-lg font-bold bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  disabled={!date || !formData.time || !formData.duration}
                >
                  Continue to Contact Details
                  <ArrowLeft className="ml-2 h-5 w-5 rotate-180" />
                </Button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Personal Information */}
                  <div className="space-y-6">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <Mail className="h-5 w-5 text-primary" />
                      Personal Information
                    </h4>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-base font-medium text-gray-700">Full Name *</Label>
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => setFormData({...formData, name: e.target.value})}
                          placeholder="Enter your full name"
                          className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-base font-medium text-gray-700">Email Address *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => setFormData({...formData, email: e.target.value})}
                          placeholder="Enter your email address"
                          className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-base font-medium text-gray-700">Phone Number *</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => setFormData({...formData, phone: e.target.value})}
                          placeholder="Enter your phone number"
                          className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Service Details */}
                  <div className="space-y-6">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                      <MapPin className="h-5 w-5 text-primary" />
                      Service Details
                    </h4>

                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="address" className="text-base font-medium text-gray-700">Service Address *</Label>
                        <Textarea
                          id="address"
                          value={formData.address}
                          onChange={(e) => setFormData({...formData, address: e.target.value})}
                          placeholder="Enter the full address where service is needed"
                          rows={4}
                          className="text-base border-2 hover:border-primary/50 focus:border-primary transition-colors resize-none"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="notes" className="text-base font-medium text-gray-700">Special Instructions</Label>
                        <Textarea
                          id="notes"
                          value={formData.notes}
                          onChange={(e) => setFormData({...formData, notes: e.target.value})}
                          placeholder="Any special requirements, access instructions, or preferences"
                          rows={4}
                          className="text-base border-2 hover:border-primary/50 focus:border-primary transition-colors resize-none"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Booking Summary */}
                <div className="bg-gradient-to-r from-primary/5 to-blue-500/5 border border-primary/20 rounded-2xl p-6">
                  <h4 className="font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-emerald-500" />
                    Final Booking Summary
                  </h4>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm text-gray-600">Service:</span>
                        <p className="font-medium text-gray-800">{service.title}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">Date & Time:</span>
                        <p className="font-medium text-gray-800">
                          {date ? format(date, "EEEE, MMMM do, yyyy") : "Not selected"} at {formData.time}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">Duration:</span>
                        <p className="font-medium text-gray-800">{formData.duration}</p>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <span className="text-sm text-gray-600">Contact:</span>
                        <p className="font-medium text-gray-800">{formData.name || "Not provided"}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">Email:</span>
                        <p className="font-medium text-gray-800">{formData.email || "Not provided"}</p>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600">Phone:</span>
                        <p className="font-medium text-gray-800">{formData.phone || "Not provided"}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Button
                    variant="outline"
                    onClick={() => setStep(1)}
                    className="flex-1 h-14 text-base border-2 hover:border-primary/50"
                  >
                    <ArrowLeft className="mr-2 h-5 w-5" />
                    Back
                  </Button>
                  <Button
                    onClick={handleSubmit}
                    className="flex-1 h-14 text-lg font-bold bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    disabled={!formData.name || !formData.email || !formData.phone || !formData.address}
                  >
                    <Mail className="mr-2 h-5 w-5" />
                    Send Booking Request
                  </Button>
                </div>
              </div>
            )}
            </CardContent>
          </Card>

          {/* Trust Indicators */}
          <div className="mt-12 grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center p-6 bg-white/60 backdrop-blur-sm border border-blue-100 rounded-2xl shadow-lg">
              <Shield className="h-8 w-8 text-emerald-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2">Background Checked</h4>
              <p className="text-sm text-gray-600">All helpers are verified and background checked</p>
            </div>
            <div className="text-center p-6 bg-white/60 backdrop-blur-sm border border-blue-100 rounded-2xl shadow-lg">
              <Star className="h-8 w-8 text-yellow-500 mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2">Highly Rated</h4>
              <p className="text-sm text-gray-600">4.9/5 average rating from satisfied customers</p>
            </div>
            <div className="text-center p-6 bg-white/60 backdrop-blur-sm border border-blue-100 rounded-2xl shadow-lg">
              <Phone className="h-8 w-8 text-primary mx-auto mb-3" />
              <h4 className="font-semibold text-gray-800 mb-2">24/7 Support</h4>
              <p className="text-sm text-gray-600">Round-the-clock customer support available</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
