@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    --primary: 199 89% 48%;
    --primary-foreground: 0 0% 100%;

    --secondary: 199 89% 96%;
    --secondary-foreground: 199 89% 20%;

    --muted: 199 20% 96%;
    --muted-foreground: 199 10% 45%;

    --accent: 199 89% 94%;
    --accent-foreground: 199 89% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 199 20% 90%;
    --input: 199 20% 90%;
    --ring: 199 89% 48%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 199 10% 26%;

    --sidebar-primary: 199 89% 48%;

    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 199 20% 95%;

    --sidebar-accent-foreground: 199 89% 20%;

    --sidebar-border: 199 20% 90%;

    --sidebar-ring: 199 89% 48%;
  }

  .dark {
    --background: 199 20% 5%;
    --foreground: 0 0% 98%;

    --card: 199 20% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 199 20% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 199 89% 60%;
    --primary-foreground: 199 20% 5%;

    --secondary: 199 20% 15%;
    --secondary-foreground: 0 0% 98%;

    --muted: 199 20% 15%;
    --muted-foreground: 199 10% 65%;

    --accent: 199 20% 15%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 199 20% 15%;
    --input: 199 20% 15%;
    --ring: 199 89% 60%;
    --sidebar-background: 199 20% 8%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 199 89% 60%;
    --sidebar-primary-foreground: 199 20% 5%;
    --sidebar-accent: 199 20% 12%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 199 20% 15%;
    --sidebar-ring: 199 89% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}