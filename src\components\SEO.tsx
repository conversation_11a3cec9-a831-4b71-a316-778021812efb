import { Helmet } from "react-helmet-async";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  structuredData?: object;
}

export const SEO = ({
  title = "KlikaHelper - Trusted Home Helpers | Cleaning, Babysitting & Household Services",
  description = "Book trusted, background-checked home helpers in South Africa. Professional cleaning services from R450, babysitting from R120/hr, and household services from R300/hr. Available 24/7 with 4.9/5 rating.",
  keywords = "home helpers South Africa, cleaning services Johannesburg, babysitting Cape Town, household services, trusted helpers, background checked cleaners, professional babysitters, home maintenance, property management services, domestic help",
  image = "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?auto=format&fit=crop&w=1200&h=600",
  url = "https://klikahelper.co.za/",
  type = "website",
  structuredData
}: SEOProps) => {
  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Klika<PERSON>elper",
    "description": "Trusted home helper network providing cleaning, babysitting, and household services in South Africa",
    "url": "https://klikahelper.co.za",
    "logo": "https://klikahelper.co.za/favicon.ico",
    "image": image,
    "telephone": "(*************",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "ZA",
      "addressRegion": "Gauteng",
      "addressLocality": "Johannesburg"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "-26.2041",
      "longitude": "28.0473"
    },
    "openingHours": "Mo-Su 00:00-23:59",
    "priceRange": "R120-R450",
    "serviceArea": {
      "@type": "Country",
      "name": "South Africa"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Home Helper Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "House Cleaning Services",
            "description": "Professional cleaning services with eco-friendly products",
            "provider": {
              "@type": "LocalBusiness",
              "name": "KlikaHelper"
            }
          },
          "price": "450",
          "priceCurrency": "ZAR",
          "priceSpecification": {
            "@type": "PriceSpecification",
            "price": "450",
            "priceCurrency": "ZAR",
            "unitText": "per visit"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Babysitting Services",
            "description": "Trusted, background-checked babysitters for your peace of mind",
            "provider": {
              "@type": "LocalBusiness",
              "name": "KlikaHelper"
            }
          },
          "price": "120",
          "priceCurrency": "ZAR",
          "priceSpecification": {
            "@type": "PriceSpecification",
            "price": "120",
            "priceCurrency": "ZAR",
            "unitText": "per hour"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Household Services",
            "description": "Complete household management and maintenance services",
            "provider": {
              "@type": "LocalBusiness",
              "name": "KlikaHelper"
            }
          },
          "price": "300",
          "priceCurrency": "ZAR",
          "priceSpecification": {
            "@type": "PriceSpecification",
            "price": "300",
            "priceCurrency": "ZAR",
            "unitText": "per hour"
          }
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "1000",
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": [
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "author": {
          "@type": "Person",
          "name": "Sarah M."
        },
        "reviewBody": "Excellent service! The cleaner was professional and thorough. Highly recommend KlikaHelper."
      }
    ],
    "sameAs": [
      "https://twitter.com/klikahelper",
      "https://facebook.com/klikahelper",
      "https://linkedin.com/company/klikahelper"
    ]
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      
      {/* Twitter */}
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
    </Helmet>
  );
};
