import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Sparkles, Clock, Star, ArrowRight, Shield, Heart, Zap, Home, CheckCircle, Baby } from "lucide-react";
import { ServiceBooking } from "@/components/ServiceBooking";
import { Header } from "@/components/Header";

const Index = () => {
  const [selectedService, setSelectedService] = useState<string | null>(null);

  const services = [
    {
      id: "cleaning",
      title: "House Cleaning",
      description: "Professional cleaning services to keep your home spotless and fresh",
      icon: Sparkles,
      price: "Starting from R450/visit",
      features: ["Eco-friendly products", "Fully insured", "Same day available", "Custom cleaning plans"],
      image: "photo-1649972904349-6e44c42644a7",
      gradient: "from-sky-400 to-blue-500"
    },
    {
      id: "babysitting",
      title: "Babysitting Services",
      description: "Trusted, background-checked babysitters for your peace of mind",
      icon: Baby,
      price: "Starting from R120/hour",
      features: ["Background checked", "CPR certified", "Flexible scheduling", "References verified"],
      image: "photo-1581091226825-a6a2a5aee158",
      gradient: "from-blue-400 to-cyan-500"
    },
    {
      id: "household",
      title: "Household Services",
      description: "Complete household management and maintenance services",
      icon: Home,
      price: "Starting from R300/hour",
      features: ["Property management", "Maintenance coordination", "Regular check-ins", "Trusted professionals"],
      image: "photo-1556909114-f6e7ad7d3136",
      gradient: "from-cyan-400 to-teal-500"
    }
  ];

  if (selectedService) {
    const service = services.find(s => s.id === selectedService);
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <ServiceBooking
          service={service!}
          onBack={() => setSelectedService(null)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50 overflow-hidden">
      <Header />

      {/* Animated Background */}
      <div className="fixed inset-0 pointer-events-none">
        <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/10 to-cyan-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-96 h-96 bg-gradient-to-r from-sky-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-gradient-to-r from-cyan-400/10 to-teal-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Gradient Mesh Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/30"></div>

        {/* Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            {/* Floating Elements */}
            <div className="absolute -top-20 left-10 w-4 h-4 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-bounce delay-300"></div>
            <div className="absolute top-32 right-20 w-6 h-6 bg-gradient-to-r from-sky-400 to-blue-400 rounded-full animate-bounce delay-700"></div>
            <div className="absolute -bottom-10 left-1/4 w-3 h-3 bg-gradient-to-r from-cyan-400 to-teal-400 rounded-full animate-bounce delay-1000"></div>

            {/* Main Title with Gradient Animation */}
            <h1 className="text-6xl md:text-8xl font-black mb-8 leading-tight">
              <span className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
                Your Trusted
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-500 via-sky-500 to-cyan-500 bg-clip-text text-transparent">
                Home Helper
              </span>
            </h1>

            {/* Subtitle with Glass Effect */}
            <div className="backdrop-blur-sm bg-white/60 border border-blue-100 rounded-2xl p-8 mb-12 max-w-4xl mx-auto shadow-lg">
              <p className="text-xl md:text-2xl text-gray-700 leading-relaxed">
                KlikaHelper makes booking trusted home helpers simple and stress-free. From <span className="text-primary font-semibold">cleaning</span> to <span className="text-primary font-semibold">babysitting</span> and household services,
                we connect you with reliable, background-checked professionals in your area.
              </p>
            </div>

            {/* CTA Button with Hover Effects */}
            <div className="mb-16">
              <Button
                size="lg"
                className="group relative px-12 py-4 text-lg font-bold bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 border-0 rounded-full shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300 hover:scale-105 transform text-white"
              >
                <span className="relative z-10 flex items-center">
                  Book a Service Now
                  <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
              </Button>
            </div>

            {/* Stats with Animated Counters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-2xl p-6 hover:bg-white/80 transition-all duration-300 group shadow-lg">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <Star className="h-6 w-6 fill-yellow-400 text-yellow-400 group-hover:animate-spin" />
                  <span className="text-2xl font-bold text-gray-800">4.9/5</span>
                </div>
                <p className="text-gray-600 text-sm font-medium">Average Rating</p>
              </div>
              <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-2xl p-6 hover:bg-white/80 transition-all duration-300 group shadow-lg">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <Clock className="h-6 w-6 text-primary group-hover:animate-spin" />
                  <span className="text-2xl font-bold text-gray-800">24/7</span>
                </div>
                <p className="text-gray-600 text-sm font-medium">Available Booking</p>
              </div>
              <div className="backdrop-blur-md bg-white/70 border border-blue-100 rounded-2xl p-6 hover:bg-white/80 transition-all duration-300 group shadow-lg">
                <div className="flex items-center justify-center gap-3 mb-2">
                  <Shield className="h-6 w-6 text-emerald-500 group-hover:animate-pulse" />
                  <span className="text-2xl font-bold text-gray-800">100%</span>
                </div>
                <p className="text-gray-600 text-sm font-medium">Verified Helpers</p>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-primary/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="relative py-32 bg-gradient-to-b from-white to-blue-50/50">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              Our
              <span className="block bg-gradient-to-r from-primary via-blue-500 to-cyan-500 bg-clip-text text-transparent">
                Services
              </span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Professional, vetted helpers ready to assist you with your daily needs.
              All our helpers are background-checked and trusted by families across South Africa.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {services.map((service, index) => {
              const IconComponent = service.icon;
              return (
                <Card
                  key={service.id}
                  className="group relative cursor-pointer transition-all duration-500 hover:scale-105 bg-white/80 backdrop-blur-xl border-blue-100 hover:border-primary/30 rounded-3xl overflow-hidden shadow-lg hover:shadow-xl"
                  onClick={() => setSelectedService(service.id)}
                >
                  {/* Gradient Border Animation */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${service.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl`}></div>

                  <CardHeader className="text-center pb-6 relative z-10">
                    {/* Animated Icon */}
                    <div className={`w-16 h-16 bg-gradient-to-r ${service.gradient} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 group-hover:scale-110 transition-all duration-300 shadow-lg`}>
                      <IconComponent className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                      {service.title}
                    </CardTitle>
                    <CardDescription className="text-base text-gray-600 leading-relaxed">
                      {service.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="space-y-6 px-6 pb-6 relative z-10">
                    {/* Image with Overlay Effects */}
                    <div className="aspect-video rounded-xl overflow-hidden relative group/img">
                      <img
                        src={`https://images.unsplash.com/${service.image}?auto=format&fit=crop&w=600&h=400`}
                        alt={service.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      <div className={`absolute inset-0 bg-gradient-to-t ${service.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
                    </div>

                    {/* Price Badge */}
                    <div className="text-center">
                      <Badge className={`text-base py-2 px-4 font-bold bg-gradient-to-r ${service.gradient} border-0 rounded-full shadow-md hover:shadow-lg transition-shadow duration-300 text-white`}>
                        {service.price}
                      </Badge>
                    </div>

                    {/* Features List */}
                    <div className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-3 text-gray-600 group/feature hover:text-gray-800 transition-colors duration-200">
                          <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0 group-hover/feature:animate-pulse" />
                          <span className="text-sm font-medium">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* CTA Button */}
                    <Button
                      className={`w-full text-base font-bold py-3 rounded-xl bg-gradient-to-r ${service.gradient} hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 border-0 group-hover:scale-105 transform text-white`}
                      size="lg"
                    >
                      <span className="flex items-center justify-center">
                        Book Now
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </span>
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Trust Section */}
      <section className="relative py-32 bg-gradient-to-b from-blue-50/50 to-white">
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_50%)]"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-6xl mx-auto text-center">
            <h3 className="text-4xl md:text-5xl font-black text-gray-800 mb-4">
              Why Choose
              <span className="block bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent">
                KlikaHelper?
              </span>
            </h3>
            <p className="text-xl text-gray-600 mb-16 max-w-2xl mx-auto">
              Experience the difference with our trusted service platform designed for busy professionals and property managers
            </p>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Shield,
                  title: "Background Checked",
                  description: "All our helpers undergo rigorous background checks and verification for your complete peace of mind",
                  gradient: "from-emerald-400 to-teal-500"
                },
                {
                  icon: Zap,
                  title: "Fast & Reliable",
                  description: "Book services that fit your schedule, with same-day availability and dependable professionals",
                  gradient: "from-blue-400 to-cyan-500"
                },
                {
                  icon: Heart,
                  title: "Satisfaction Guaranteed",
                  description: "100% satisfaction guarantee on all services - we're committed to exceeding your expectations",
                  gradient: "from-pink-400 to-rose-500"
                }
              ].map((item, index) => (
                <div key={index} className="group relative">
                  {/* Glowing Background */}
                  <div className={`absolute inset-0 bg-gradient-to-r ${item.gradient} opacity-0 group-hover:opacity-5 blur-xl transition-opacity duration-500 rounded-3xl`}></div>

                  <div className="relative backdrop-blur-sm bg-white/80 border border-blue-100 rounded-3xl p-8 hover:bg-white/90 transition-all duration-500 group-hover:scale-105 transform shadow-lg">
                    <div className={`w-16 h-16 bg-gradient-to-r ${item.gradient} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform duration-300 shadow-lg`}>
                      <item.icon className="h-8 w-8 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-gray-800 mb-4 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                      {item.title}
                    </h4>
                    <p className="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Footer CTA */}
      <section className="relative py-20 bg-gradient-to-r from-primary via-blue-500 to-cyan-500">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Experience Trusted Home Help?
          </h3>
          <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust KlikaHelper for their home service needs.
          </p>
          <Button
            size="lg"
            className="bg-white text-primary hover:bg-gray-50 font-bold px-12 py-4 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-105 transform"
          >
            Get Started Today
            <ArrowRight className="ml-3 h-5 w-5" />
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;