# 🏠 KlikaHelper

**Your Trusted Home Helper Network**

KlikaHelper is a modern, responsive web application that connects busy professionals and property managers with reliable, background-checked home helpers. Our platform simplifies the booking process for essential household services including cleaning, babysitting, and general household maintenance.

![KlikaHelper Preview](https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?auto=format&fit=crop&w=1200&h=600)

[![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.3.1-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4.1-646CFF?style=for-the-badge&logo=vite&logoColor=white)](https://vitejs.dev/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.11-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)
[![shadcn/ui](https://img.shields.io/badge/shadcn%2Fui-Latest-000000?style=for-the-badge&logo=shadcnui&logoColor=white)](https://ui.shadcn.com/)

## 🌟 Live Demo

> **Note**: Add your live demo URL here when deployed

## 📋 Table of Contents

- [🌟 Live Demo](#-live-demo)
- [✨ Features](#-features)
- [🎨 Design Philosophy](#-design-philosophy)
- [🚀 Quick Start](#-quick-start)
  - [Prerequisites](#prerequisites)
  - [Installation](#installation)
  - [Development](#development)
  - [Build for Production](#build-for-production)
- [🛠️ Tech Stack](#️-tech-stack)
- [📁 Project Structure](#-project-structure)
- [🔧 Configuration](#-configuration)
- [📧 Contact Integration](#-contact-integration)
- [🎯 Key Features Explained](#-key-features-explained)
- [� Screenshots](#-screenshots)
- [�🚀 Deployment](#-deployment)
- [🧪 Testing](#-testing)
- [🔍 Troubleshooting](#-troubleshooting)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [🙏 Acknowledgments](#-acknowledgments)

## ✨ Features

- **🧹 House Cleaning Services** - Professional cleaning with eco-friendly products
- **👶 Babysitting Services** - Trusted, background-checked babysitters
- **🏡 Household Services** - Complete household management and maintenance
- **📱 Easy Booking System** - Intuitive multi-step booking process
- **📧 Email Integration** - Seamless booking requests via email
- **🛡️ Background Checked** - All helpers are verified and background-checked
- **⭐ Highly Rated** - 4.9/5 average rating from satisfied customers
- **📞 24/7 Support** - Round-the-clock customer support
- **🎨 Modern UI/UX** - Built with shadcn/ui components for consistent design
- **📱 Responsive Design** - Optimized for all devices and screen sizes
- **🌙 Theme Support** - Light and dark mode compatibility
- **♿ Accessibility** - WCAG compliant with keyboard navigation support

## 🎨 Design

- **Light Blue & White Theme** - Clean, professional, and trustworthy design
- **Responsive Design** - Works perfectly on all devices
- **Modern UI/UX** - Built with shadcn/ui components
- **Smooth Animations** - Engaging user experience with Tailwind CSS animations
- **Accessibility First** - Designed with accessibility in mind
- **Component-Based Architecture** - Reusable and maintainable UI components
- **Type-Safe Styling** - CSS-in-TS with Tailwind CSS and class-variance-authority

## 🚀 Quick Start

### Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (version 18.0.0 or higher) - [Download here](https://nodejs.org/)
- **npm** (comes with Node.js) or **yarn** or **bun** as package manager
- **Git** for version control - [Download here](https://git-scm.com/)

You can verify your installations by running:
```bash
node --version  # Should be 18.0.0 or higher
npm --version   # Should be 8.0.0 or higher
git --version   # Any recent version
```

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/klik-a-helper-squad.git
   cd klik-a-helper-squad
   ```

2. **Install dependencies**
   ```bash
   # Using npm (recommended)
   npm install

   # Or using yarn
   yarn install

   # Or using bun (fastest)
   bun install
   ```

### Development

1. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   bun dev
   ```

2. **Open your browser**

   The application will be available at `http://localhost:5173`

3. **Development features**
   - ⚡ Hot Module Replacement (HMR) for instant updates
   - 🔍 TypeScript type checking
   - 🎨 Tailwind CSS with JIT compilation
   - 📱 Responsive design preview

### Build for Production

```bash
# Build the application
npm run build

# Preview the production build locally
npm run preview

# Build for development (with source maps)
npm run build:dev
```

The built files will be in the `dist/` directory, ready for deployment.

## 🛠️ Tech Stack

This project is built with modern, industry-standard technologies:

### Core Framework
- **⚡ Vite** (v5.4.1) - Lightning fast build tool and dev server
- **📘 TypeScript** (v5.5.3) - Type-safe JavaScript with excellent IDE support
- **⚛️ React 18** (v18.3.1) - Modern React with concurrent features and hooks

### Styling & UI
- **🎨 Tailwind CSS** (v3.4.11) - Utility-first CSS framework
- **🧩 shadcn/ui** - Beautiful, accessible, and customizable UI components
- **🎭 Radix UI** - Unstyled, accessible components for design systems
- **✨ Tailwind CSS Animate** - Animation utilities for Tailwind
- **🎯 Lucide React** (v0.462.0) - Beautiful & consistent icon library
- **🌙 next-themes** - Theme switching with system preference support

### Form Handling & Validation
- **📝 React Hook Form** (v7.53.0) - Performant forms with easy validation
- **🔍 Zod** (v3.23.8) - TypeScript-first schema validation
- **� @hookform/resolvers** - Validation library resolvers for React Hook Form

### Routing & Navigation
- **�📱 React Router DOM** (v6.26.2) - Declarative routing for React applications

### Data & State Management
- **🔄 TanStack Query** (v5.56.2) - Powerful data synchronization for React
- **📅 date-fns** (v3.6.0) - Modern JavaScript date utility library
- **📊 Recharts** (v2.12.7) - Composable charting library for React

### Development Tools
- **🔧 ESLint** (v9.9.0) - Code linting and formatting
- **🎯 TypeScript ESLint** - TypeScript-specific linting rules
- **📦 PostCSS** - CSS transformation tool
- **🚀 SWC** - Fast TypeScript/JavaScript compiler

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # shadcn/ui components
│   ├── Header.tsx      # Navigation header
│   └── ServiceBooking.tsx # Booking form component
├── pages/              # Page components
│   ├── Index.tsx       # Home page
│   └── NotFound.tsx    # 404 page
├── hooks/              # Custom React hooks
├── lib/                # Utility functions
└── main.tsx           # Application entry point
```

## 📧 Contact Integration

The booking system integrates with email for seamless communication:

- **Email**: <EMAIL>
- **Phone**: (*************
- Booking requests are automatically formatted and sent via mailto links

## 🎯 Key Features Explained

### Multi-Step Booking Process
1. **Service Selection** - Choose from cleaning, babysitting, or household services
2. **Date & Time** - Select preferred date, time, and duration
3. **Contact Information** - Provide personal and service details
4. **Email Confirmation** - Automatic email generation with booking details

### Service Categories
- **House Cleaning** - Starting from R450/visit
- **Babysitting** - Starting from R120/hour
- **Household Services** - Starting from R300/hour

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Netlify
```bash
# Build the project
npm run build

# Deploy the dist folder to Netlify
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)
- Images from [Unsplash](https://unsplash.com/)

---

**KlikaHelper** - Making trusted home help just a few clicks away! 🏠✨
