import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Search, Calendar, CheckCircle, Star, Shield, Clock, Users, Phone, Mail } from "lucide-react";
import { Header } from "@/components/Header";
import { SEO } from "@/components/SEO";

const HowItWorks = () => {
  const steps = [
    {
      step: 1,
      title: "Browse Services",
      description: "Choose from our range of trusted home services including cleaning, babysitting, and household maintenance.",
      icon: Search,
      details: ["View service descriptions", "Check pricing", "Read helper profiles", "See ratings & reviews"],
      color: "from-blue-400 to-cyan-500"
    },
    {
      step: 2,
      title: "Book Your Service",
      description: "Select your preferred date, time, and duration. Fill in your contact details and service requirements.",
      icon: Calendar,
      details: ["Pick date & time", "Choose duration", "Add special instructions", "Provide contact info"],
      color: "from-cyan-400 to-teal-500"
    },
    {
      step: 3,
      title: "Get Matched",
      description: "We'll match you with a verified, background-checked helper who meets your specific needs.",
      icon: Users,
      details: ["Background verification", "Skill matching", "Availability check", "Helper confirmation"],
      color: "from-teal-400 to-emerald-500"
    },
    {
      step: 4,
      title: "Service Delivered",
      description: "Your helper arrives on time and delivers exceptional service. Rate your experience when complete.",
      icon: CheckCircle,
      details: ["On-time arrival", "Professional service", "Quality assurance", "Rate & review"],
      color: "from-emerald-400 to-green-500"
    }
  ];

  const features = [
    {
      icon: Shield,
      title: "Background Checked",
      description: "All helpers undergo comprehensive background checks and identity verification."
    },
    {
      icon: Star,
      title: "Highly Rated",
      description: "Only helpers with excellent ratings and reviews are part of our network."
    },
    {
      icon: Clock,
      title: "Flexible Scheduling",
      description: "Book services that fit your schedule, including same-day availability."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50">
      <SEO
        title="How It Works - KlikaHelper | Simple 4-Step Process"
        description="Learn how KlikaHelper works: Browse services, book your preferred time, get matched with verified helpers, and enjoy professional service. Simple, fast, and reliable."
        url="https://klikahelper.co.za/how-it-works"
        keywords="how KlikaHelper works, booking process, home helper booking, service booking steps, trusted helpers process"
      />
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/30"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-gradient-to-r from-primary to-blue-500 text-white border-0 px-6 py-2 text-base font-semibold">
              How It Works
            </Badge>
            <h1 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              Getting Help is
              <span className="block bg-gradient-to-r from-primary via-blue-500 to-cyan-500 bg-clip-text text-transparent">
                Simple & Easy
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              From booking to completion, we've streamlined the process to make getting trusted home help as easy as a few clicks.
            </p>
          </div>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-20 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-8 md:gap-12">
              {steps.map((step, index) => {
                const IconComponent = step.icon;
                const isEven = index % 2 === 1;
                
                return (
                  <div key={step.step} className={`flex flex-col ${isEven ? 'lg:flex-row-reverse' : 'lg:flex-row'} items-center gap-8 lg:gap-16`}>
                    {/* Content */}
                    <div className="flex-1 text-center lg:text-left">
                      <div className="flex items-center justify-center lg:justify-start gap-4 mb-6">
                        <div className={`w-12 h-12 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                          {step.step}
                        </div>
                        <h3 className="text-3xl font-bold text-gray-800">{step.title}</h3>
                      </div>
                      
                      <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                        {step.description}
                      </p>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {step.details.map((detail, detailIndex) => (
                          <div key={detailIndex} className="flex items-center gap-2 text-gray-600">
                            <CheckCircle className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                            <span className="text-sm font-medium">{detail}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Visual */}
                    <div className="flex-1 flex justify-center">
                      <div className={`relative w-64 h-64 bg-gradient-to-r ${step.color} rounded-3xl flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300`}>
                        <IconComponent className="w-24 h-24 text-white" />
                        <div className={`absolute inset-0 bg-gradient-to-r ${step.color} rounded-3xl blur opacity-30 animate-pulse`}></div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-black text-gray-800 mb-6">
                Why Choose
                <span className="block bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent">
                  KlikaHelper?
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                We're committed to providing you with the highest quality service and peace of mind.
              </p>
            </div>
            
            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="group relative bg-white/80 backdrop-blur-sm border-blue-100 hover:border-primary/30 rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardHeader className="text-center pb-6">
                    <div className="w-16 h-16 bg-gradient-to-r from-primary to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform duration-300 shadow-lg">
                      <feature.icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-800 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 leading-relaxed text-center">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary via-blue-500 to-cyan-500">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Get Started?
          </h3>
          <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied customers who trust KlikaHelper for their home service needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="bg-white text-primary hover:bg-gray-50 font-bold px-12 py-4 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-105 transform"
            >
              Book a Service Now
              <ArrowRight className="ml-3 h-5 w-5" />
            </Button>
            <div className="flex items-center gap-4 text-white">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="font-medium">(*************</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="font-medium"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HowItWorks;
