import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Heart, Users, Shield, Award, Target, Zap, Phone, Mail } from "lucide-react";
import { Header } from "@/components/Header";
import { SEO } from "@/components/SEO";

const About = () => {
  const values = [
    {
      icon: Heart,
      title: "Care & Compassion",
      description: "We genuinely care about making your life easier and your home a better place to live.",
      color: "from-pink-400 to-rose-500"
    },
    {
      icon: Shield,
      title: "Trust & Safety",
      description: "Every helper is thoroughly vetted, background-checked, and verified for your peace of mind.",
      color: "from-emerald-400 to-teal-500"
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We maintain the highest standards of service quality and customer satisfaction.",
      color: "from-blue-400 to-cyan-500"
    },
    {
      icon: Zap,
      title: "Reliability",
      description: "Dependable service you can count on, delivered consistently and on time.",
      color: "from-yellow-400 to-orange-500"
    }
  ];

  const stats = [
    { number: "500+", label: "Happy Customers", icon: Users },
    { number: "50+", label: "Verified Helpers", icon: Shield },
    { number: "4.9/5", label: "Average Rating", icon: Award },
    { number: "24/7", label: "Customer Support", icon: Target }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50">
      <SEO
        title="About KlikaHelper - Our Story & Values | Trusted Home Helper Network"
        description="Learn about KlikaHelper's mission to connect families with trusted, background-checked home helpers. Founded in 2020, serving 10,000+ happy customers with 4.9/5 rating."
        url="https://klikahelper.co.za/about"
        keywords="about KlikaHelper, company story, home helper network, trusted service provider, background checked helpers, South Africa home services"
      />
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/30"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-gradient-to-r from-primary to-blue-500 text-white border-0 px-6 py-2 text-base font-semibold">
              About KlikaHelper
            </Badge>
            <h1 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              Making Homes
              <span className="block bg-gradient-to-r from-primary via-blue-500 to-cyan-500 bg-clip-text text-transparent">
                Happier Places
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Founded in 2025, KlikaHelper was created after we noticed how many homeowners were struggling to find trusted, reliable help. We stepped in to make that easier — with vetted professionals and an easy-to-use platform that delivers peace of mind.
            </p>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-black text-gray-800 mb-6">
                Our
                <span className="block bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent">
                  Values
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                These core values guide everything we do and every decision we make.
              </p>
            </div>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => (
                <Card key={index} className="group relative bg-white/80 backdrop-blur-sm border-blue-100 hover:border-primary/30 rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardHeader className="text-center pb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${value.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform duration-300 shadow-lg`}>
                      <value.icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-lg font-bold text-gray-800 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                      {value.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 leading-relaxed text-center text-sm">
                      {value.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-gray-800 mb-6">
                Our Impact
              </h2>
              <p className="text-xl text-gray-600">
                Numbers that reflect our commitment to excellence
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center group">
                  <div className="w-20 h-20 bg-gradient-to-r from-primary to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                    <stat.icon className="w-10 h-10 text-white" />
                  </div>
                  <div className="text-4xl font-black text-gray-800 mb-2">{stat.number}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary via-blue-500 to-cyan-500">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Join the KlikaHelper Family
          </h3>
          <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
            Experience the difference that trusted, professional home help can make in your life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="bg-white text-primary hover:bg-gray-50 font-bold px-12 py-4 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-105 transform"
            >
              Book Your First Service
              <ArrowRight className="ml-3 h-5 w-5" />
            </Button>
            <div className="flex items-center gap-4 text-white">
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                <span className="font-medium">(*************</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                <span className="font-medium"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
